from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, status
from .dependencies import verify_project_access
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional                   
from pydantic import BaseModel, field_validator
from datetime import datetime, timedelta, timezone
from jose import jwt, JWTError
from passlib.context import CryptContext
from .database import get_db
from .models import (
    User, Project, Issue, Solution, KnowledgeBase, CategoryEnum, RoleEnum, SystemNameEnum,
    Admin
)
from .schemas import UserCreate, UserUpdate, UserResponse, UserLogin, UserProfileResponse
from zoneinfo import ZoneInfo
from fastapi.responses import JSONResponse
import logging
from .dependencies import get_current_user, require_admin
from .routers import search, global_search, favorites, knowledge_base, projects
from .routers.knowledge_base import KnowledgeBaseCreate
from . import crud

# Security configuration
from .config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

app = FastAPI(title="Exception Tracker API")

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Restrict to frontend origin
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["Content-Type", "Authorization"],
)

# Include routers
app.include_router(search.router)
app.include_router(global_search.router)
app.include_router(favorites.router)
app.include_router(knowledge_base.router)
app.include_router(projects.router)
app.include_router(projects.router)

class ProjectBase(BaseModel):
    project_name: str

class ProjectCreate(ProjectBase):
    pass

class ProjectResponse(ProjectBase):
    project_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class IssueBase(BaseModel):
    project_id: int
    category: CategoryEnum
    title: str
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    system_name: Optional[SystemNameEnum] = None
    issue_type: Optional[str] = None  # Changed from 'type' to 'issue_type'
    status: str = 'Open'  # This should be an enum in a real-world scenario
    impact: Optional[str] = None  # Can be 'Low', 'Medium', 'High', 'Critical'
    frequency: Optional[str] = None  # Can be 'Rare', 'Occasional', 'Frequent', 'Always'
    description: str
    jira_id: Optional[str] = None
    jira_link: Optional[str] = None
    hemants_view: Optional[str] = None

class IssueCreate(IssueBase):
    pass

class IssueResponse(IssueBase):
    issue_id: int
    created_at: datetime
    created_by: int
    solutions: Optional[List["SolutionResponse"]] = []

    class Config:
        from_attributes = True

class SolutionBase(BaseModel):
    issue_id: int
    category: CategoryEnum
    solution_text: str
    provided_by: Optional[int] = None
    
    class Config:
        json_encoders = {
            CategoryEnum: lambda v: v.value  # Ensure proper JSON serialization of the enum
        }

class SolutionCreate(SolutionBase):
    # Add validation for category
    @field_validator('category', mode='before')
    def validate_category(cls, v):
        if isinstance(v, str):
            try:
                return CategoryEnum(v.upper())
            except ValueError:
                valid_values = [e.value for e in CategoryEnum]
                raise ValueError(f"Invalid category. Must be one of: {', '.join(valid_values)}")
        return v

class SolutionResponse(SolutionBase):
    solution_id: int
    upvotes: int
    created_at: datetime
    provided_by_name: Optional[str] = None  # Add field for provider's name

    class Config:
        from_attributes = True



class KnowledgeBaseResponse(BaseModel):
    kb_id: int
    project_id: int
    title: str
    content: str
    solution: Optional[str] = None
    uploaded_by: int
    file_url: Optional[str] = None
    created_at: datetime
    author_name: Optional[str] = None
    verified: Optional[bool] = None
    views: Optional[int] = None
    likes: Optional[int] = None
    system: Optional[str] = None
    type: Optional[str] = None
    is_favorite: Optional[bool] = None
    is_liked: Optional[bool] = None

    class Config:
        from_attributes = True

class UserProjectRoleCreate(BaseModel):
    user_id: int
    project_id: int
    role: RoleEnum

class Token(BaseModel):
    access_token: str
    token_type: str



def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@app.get("/")
async def root():
    return {"message": "Exception Tracker API is running"}

# User Management Routes
@app.post("/users", response_model=UserResponse)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    try:
        db_user = User(**user.model_dump())
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

# require_admin function is now imported from dependencies.py

# Protect admin-only endpoints like this:
@app.get("/admin/users", response_model=List[UserResponse])
async def get_all_users(
    db: Session = Depends(get_db),
    _: User = Depends(require_admin)
):
    return db.query(User).all()


# Example of protecting an admin endpoint:
@app.get("/users", response_model=List[UserResponse])
async def get_users(
    db: Session = Depends(get_db),
    _: User = Depends(require_admin)  # This ensures only admins can access
):
    try:
        users = db.query(User).all()
        return users
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        # Handle the special case of "me" to return the current user
        if user_id == "me":
            return current_user

        # Otherwise, try to parse the user_id as an integer
        try:
            user_id_int = int(user_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid user ID: {user_id}. Must be an integer or 'me'."
            )

        user = db.query(User).filter(User.user_id == user_id_int).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Project Management Routes
@app.post("/projects", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        db_project = Project(**project.model_dump())
        db.add(db_project)
        db.commit()
        db.refresh(db_project)
        return db_project
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/projects", response_model=List[ProjectResponse])
async def get_projects(
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        projects = db.query(Project).all()
        return projects
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/public/projects", response_model=List[ProjectResponse])
async def get_public_projects(
    db: Session = Depends(get_db)
):
    """
    Public endpoint for getting projects without authentication.
    This is used on the homepage before login.
    """
    try:
        projects = db.query(Project).all()
        return projects
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Issue Management Routes
@app.post("/issues/", response_model=IssueResponse)
async def create_issue(
    issue: IssueCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    print(f"Received issue creation request with project_id: {issue.project_id}")
    # Verify project access
    await verify_project_access(issue.project_id, db, current_user)
    
    # Check if project exists
    project = db.query(Project).filter(Project.project_id == issue.project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Create new issue
    db_issue = Issue(
        project_id=issue.project_id,
        category=issue.category,
        title=issue.title,
        error_code=issue.error_code,
        error_message=issue.error_message,
        system_name=issue.system_name,
        issue_type=issue.issue_type,
        status=issue.status,
        impact=issue.impact,
        frequency=issue.frequency,
        description=issue.description,
        jira_id=issue.jira_id,
        jira_link=issue.jira_link,
        hemants_view=issue.hemants_view,
        created_by=current_user.user_id
    )

    try:
        db.add(db_issue)
        db.commit()
        db.refresh(db_issue)
        return db_issue
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/issues", response_model=List[IssueResponse])
async def get_issues(
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        # Use crud function instead of direct query
        issues = crud.get_all_issues(db)
        return issues
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/issues/recent", response_model=List[IssueResponse])
async def get_recent_issues(
    limit: int = 5,
    project_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        query = db.query(Issue)
        
        if project_id:
            query = query.filter(Issue.project_id == project_id)
            
        issues = query.order_by(Issue.created_at.desc()).limit(limit).all()
        return [IssueResponse.from_orm(issue) for issue in issues]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/{project_id}/issues", response_model=List[IssueResponse], operation_id="get_project_specific_issues")
async def get_project_issues(
    project_id: int,
    db: Session = Depends(get_db),
    _: User = Depends(verify_project_access)
):
    try:
        # Get the issues with their solutions
        issues = db.query(Issue).options(joinedload(Issue.solutions))\
            .filter(Issue.project_id == project_id).all()
        
        # Convert SQLAlchemy models to Pydantic models
        return [
            IssueResponse(
                issue_id=issue.issue_id,
                project_id=issue.project_id,
                category=issue.category,
                title=issue.title,
                error_code=issue.error_code,
                error_message=issue.error_message,
                system_name=issue.system_name,
                issue_type=issue.issue_type,
                status=issue.status,
                impact=issue.impact,
                frequency=issue.frequency,
                description=issue.description,
                jira_id=issue.jira_id,
                jira_link=issue.jira_link,
                hemants_view=issue.hemants_view,
                created_by=issue.created_by,
                created_at=issue.created_at,
                solutions=[
                    SolutionResponse(
                        solution_id=sol.solution_id,
                        issue_id=sol.issue_id,
                        category=sol.category,
                        solution_text=sol.solution_text,
                        provided_by=sol.provided_by,
                        upvotes=sol.upvotes,
                        created_at=sol.created_at,
                        provided_by_name=sol.creator.name if sol.creator else None
                    ) for sol in issue.solutions
                ]
            ) for issue in issues
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/issues/{issue_id}", response_model=IssueResponse)
async def get_issue(
    issue_id: int,
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        # Get the issue with its solutions
        issue = db.query(Issue).options(joinedload(Issue.solutions))\
            .filter(Issue.issue_id == issue_id).first()
        
        if not issue:
            raise HTTPException(status_code=404, detail="Issue not found")
        
        # Convert SQLAlchemy model to Pydantic model
        return IssueResponse(
            issue_id=issue.issue_id,
            project_id=issue.project_id,
            category=issue.category,
            title=issue.title,
            error_code=issue.error_code,
            error_message=issue.error_message,
            system_name=issue.system_name,
            issue_type=issue.issue_type,
            status=issue.status,
            impact=issue.impact,
            frequency=issue.frequency,
            description=issue.description,
            jira_id=issue.jira_id,
            jira_link=issue.jira_link,
            hemants_view=issue.hemants_view,
            created_by=issue.created_by,
            created_at=issue.created_at,
            solutions=[
                SolutionResponse(
                    solution_id=sol.solution_id,
                    issue_id=sol.issue_id,
                    category=sol.category,
                    solution_text=sol.solution_text,
                    provided_by=sol.provided_by,
                    upvotes=sol.upvotes,
                    created_at=sol.created_at,
                    provided_by_name=sol.creator.name if sol.creator else None
                ) for sol in issue.solutions
            ]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Solution Management Routes
class SolutionListResponse(BaseModel):
    items: List[SolutionResponse]
    total: int

@app.post("/solutions", response_model=SolutionResponse)
async def create_solution(
    solution: SolutionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        # Log the incoming solution data for debugging
        print(f"Creating solution with data: {solution}")
        
        # Create a dictionary from the solution model
        solution_data = solution.model_dump()
        print(f"Solution data after model_dump: {solution_data}")

        # Always use the current user's ID as the provider
        solution_data["provided_by"] = current_user.user_id
        
        # Ensure the category is a valid CategoryEnum value
        if 'category' in solution_data and isinstance(solution_data['category'], str):
            try:
                solution_data['category'] = CategoryEnum(solution_data['category'].upper())
            except ValueError as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid category value: {solution_data['category']}. Must be one of: {', '.join([e.value for e in CategoryEnum])}"
                )

        try:
            # Create the solution
            db_solution = Solution(**solution_data)
            db.add(db_solution)
            db.commit()
            db.refresh(db_solution)
            print(f"Successfully created solution with ID: {db_solution.solution_id}")
        except Exception as db_error:
            db.rollback()
            print(f"Database error when creating solution: {str(db_error)}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create solution: {str(db_error)}"
            )

        # Add the provider's name to the response
        solution_dict = {
            "solution_id": db_solution.solution_id,
            "issue_id": db_solution.issue_id,
            "category": db_solution.category,
            "provided_by": db_solution.provided_by,
            "solution_text": db_solution.solution_text,
            "upvotes": db_solution.upvotes,
            "created_at": db_solution.created_at,
            "provided_by_name": current_user.name
        }

        return solution_dict
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        print(f"Unexpected error in create_solution: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {str(e)}"
        )
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/solutions", response_model=SolutionListResponse)
async def get_solutions(
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        solutions = db.query(Solution).all()
        # Format the response to match the expected structure
        return {"items": solutions, "total": len(solutions)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/issues/{issue_id}/solutions", response_model=SolutionListResponse)
async def get_issue_solutions(
    issue_id: int,
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        # First check if the issue exists
        issue = db.query(Issue).filter(Issue.issue_id == issue_id).first()
        if not issue:
            raise HTTPException(status_code=404, detail="Issue not found")

        # Get all solutions for this issue with user information
        from sqlalchemy.orm import joinedload
        solutions = db.query(Solution).filter(Solution.issue_id == issue_id).options(joinedload(Solution.provider)).all()

        # Add the provider's name to each solution
        result = []
        for solution in solutions:
            solution_dict = {
                "solution_id": solution.solution_id,
                "issue_id": solution.issue_id,
                "category": solution.category,
                "provided_by": solution.provided_by,
                "solution_text": solution.solution_text,
                "upvotes": solution.upvotes,
                "created_at": solution.created_at,
                "provided_by_name": solution.provider.name if solution.provider else None
            }
            result.append(solution_dict)

        # Return in the format expected by the frontend
        return {"items": result, "total": len(result)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/solutions/{solution_id}", response_model=SolutionResponse)
async def get_solution(
    solution_id: int,
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        from sqlalchemy.orm import joinedload
        solution = db.query(Solution).filter(Solution.solution_id == solution_id).options(joinedload(Solution.provider)).first()
        if not solution:
            raise HTTPException(status_code=404, detail="Solution not found")

        # Add the provider's name to the solution
        solution_dict = {
            "solution_id": solution.solution_id,
            "issue_id": solution.issue_id,
            "category": solution.category,
            "provided_by": solution.provided_by,
            "solution_text": solution.solution_text,
            "upvotes": solution.upvotes,
            "created_at": solution.created_at,
            "provided_by_name": solution.provider.name if solution.provider else None
        }

        return solution_dict
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Knowledge Base Routes
@app.post("/knowledge-base", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    kb: KnowledgeBaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        # Added user ID injection and solution validation
        kb_data = kb.model_dump()
        kb_data["uploaded_by"] = current_user.user_id
        if kb_data.get("solution") == "":
            kb_data["solution"] = None
        
        db_kb = KnowledgeBase(**kb_data)
        db.add(db_kb)
        db.commit()
        db.refresh(db_kb)
        return db_kb
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/knowledge-base", response_model=List[KnowledgeBaseResponse])
async def get_knowledge_base(
    project_id: Optional[int] = None,
    db: Session = Depends(get_db),
    _current_user: User = Depends(get_current_user)
):
    try:
        query = db.query(KnowledgeBase)
        if project_id is not None:
            query = query.filter(KnowledgeBase.project_id == project_id)
        kb_entries = query.all()
        return kb_entries
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/knowledge-base/{kb_id}", response_model=KnowledgeBaseResponse, operation_id="get_kb_entry_by_id")
async def get_knowledge_base_entry(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        kb_entry = db.query(KnowledgeBase).filter(KnowledgeBase.kb_id == kb_id).first()
        if not kb_entry:
            raise HTTPException(status_code=404, detail="Knowledge base entry not found")
        return kb_entry
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Authentication routes
# Update the login endpoint
@app.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    try:
        user = db.query(User).filter(User.email == form_data.username).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not verify_password(form_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Use joinedload to ensure admin relationship is loaded
        from sqlalchemy.orm import joinedload
        user = db.query(User).options(joinedload(User.admin)).filter(User.email == form_data.username).first()

        # Store admin status
        is_admin = user.admin is not None

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": user.email,
                "is_admin": is_admin,
                "user_id": user.user_id
            },
            expires_delta=access_token_expires
        )

        response_data = {
            "access_token": access_token,
            "token_type": "bearer",
            "is_admin": is_admin,  # Use the stored value
            "user_id": user.user_id,
            "name": user.name,
            "message": "Successfully logged in"
        }

        response = JSONResponse(content=response_data)
        response.set_cookie(
            key="access_token",
            value=f"Bearer {access_token}",
            httponly=True,
            secure=True,
            samesite="lax",
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            path="/",
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )

@app.post("/refresh-token")
async def refresh_access_token(
    request: Request,
    db: Session = Depends(get_db)
):
    # Get the token from the cookie
    token_cookie = request.cookies.get("access_token")
    if not token_cookie or not token_cookie.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = token_cookie.replace("Bearer ", "")

    try:
        # Decode the token without verifying expiration
        payload = jwt.decode(
            token,
            SECRET_KEY,
            algorithms=[ALGORITHM],
            options={"verify_exp": False}
        )

        # Check if token is too old (e.g., more than 7 days)
        token_issued_at = datetime.fromtimestamp(payload.get("iat", 0))
        if datetime.now(timezone.utc) - token_issued_at > timedelta(days=7):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token too old, please login again",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Get user from token data with admin relationship eagerly loaded
        email = payload.get("sub")
        from sqlalchemy.orm import joinedload
        user = db.query(User).options(joinedload(User.admin)).filter(User.email == email).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Store admin status
        is_admin = user.admin is not None

        # Create new access token using the stored admin status
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": user.email,
                "is_admin": is_admin,  # Use the stored value
                "user_id": user.user_id
            },
            expires_delta=access_token_expires
        )

        response_data = {
            "access_token": access_token,
            "token_type": "bearer",
            "is_admin": is_admin,  # Use the stored value
            "user_id": user.user_id,
            "name": user.name,
        }

        response = JSONResponse(content=response_data)
        response.set_cookie(
            key="access_token",
            value=f"Bearer {access_token}",
            httponly=True,
            secure=True,
            samesite="lax",
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            path="/",
        )

        return response

    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Updated registration endpoint
@app.post("/register", response_model=UserResponse)
async def register_user(
    user: UserLogin,
    db: Session = Depends(get_db)
):
    try:
        logging.info(f"Attempting registration for email: {user.email}") # Log received email
        # Check if user already exists
        db_user = db.query(User).filter(User.email == user.email).first()

        # Log whether a user was found
        if db_user:
            logging.warning(f"User found for email {user.email}. User ID: {db_user.user_id}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": "Email already registered"}
            )
        else:
            logging.info(f"No existing user found for email {user.email}. Proceeding with registration.")

        # Create new user with hashed password
        hashed_password = get_password_hash(user.password)
        db_user = User(
            email=user.email,
            password_hash=hashed_password,
            name=user.name
        )

        # Add and commit the user first
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # If role is admin, create admin entry
        if user.role.lower() == "admin":
            try:
                # This line requires 'Admin' to be imported correctly
                admin = Admin(user_id=db_user.user_id)
                db.add(admin)
                db.commit()
            except Exception as e:
                db.rollback()
                # This is where the error message originates
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"detail": f"Failed to create admin: {str(e)}"}
                )

        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "user_id": db_user.user_id,
                "created_at": db_user.created_at.isoformat(),
                "message": "User registered successfully"
            }
        )

    except Exception as e:
        db.rollback()
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Registration failed: {str(e)}"}
        )


# Endpoint moved to router file
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from .database import get_db
from .models import User
from backend.schemas import UserCreate, UserUpdate, UserResponse, UserLogin, UserProfileResponse
from backend.auth import get_current_user, is_admin

router = APIRouter(prefix="/api/admin")

@router.get("/users", response_model=List[UserResponse])
async def get_users(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    users = db.query(User).all()
    return users

@router.post("/users", response_model=UserResponse)
async def create_user(user: UserCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    # Check if user with same email already exists
    if db.query(User).filter(User.email == user.email).first():
        raise HTTPException(status_code=400, detail="Email already registered")
    
    new_user = User(
        name=user.name,
        email=user.email,
        is_admin=user.role.lower() == "admin",
        status="Active",
        projects=user.projects
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    return new_user

@router.put("/users/{user_id}/role")
async def update_user_role(user_id: int, role: dict, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.is_admin = role["role"].lower() == "admin"
    db.commit()
    return {"message": "User role updated successfully"}

@router.put("/users/{user_id}/status")
async def update_user_status(user_id: int, status: dict, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.status = status["status"]
    db.commit()
    return {"message": "User status updated successfully"}


# Endpoint moved to router file
