import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function GET(request: NextRequest) {
  try {
    // Get the search parameters
    const searchParams = request.nextUrl.searchParams;
    const filter = searchParams.get('filter') || 'all';
    const category = searchParams.get('category') || 'all';
    const projectId = searchParams.get('project_id');
    const search = searchParams.get('search');
    const systems = searchParams.get('systems');
    const types = searchParams.get('types');
    const verification = searchParams.get('verification');
    const sortBy = searchParams.get('sort_by');

    console.log("API route received project_id:", projectId);

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Build the backend API URL with query parameters
    let apiUrl;
    const apiParams = new URLSearchParams();
    
    // Use the project-specific endpoint if projectId is provided
    if (projectId) {
      apiUrl = `${API_BASE_URL}/projects/${projectId}/knowledge-base`;
      console.log(`Using project-specific endpoint for project ID: ${projectId}`);
    } else {
      apiUrl = `${API_BASE_URL}/knowledge-base`;
    }
    
    if (filter && filter !== 'all') apiParams.append('filter', filter);
    if (category && category !== 'all') apiParams.append('category', category);
    if (search) apiParams.append('search', search);
    if (systems) apiParams.append('systems', systems);
    if (types) apiParams.append('types', types);
    if (verification && verification !== 'all') apiParams.append('verification', verification);
    if (sortBy && sortBy !== 'relevance') apiParams.append('sort_by', sortBy);

    if (apiParams.toString()) {
      apiUrl += `?${apiParams.toString()}`;
    }

    console.log(`Proxying request to: ${apiUrl}`);

    // Make the request to the backend API
    // Ensure we're using the router-based endpoint that correctly filters by project_id
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to fetch knowledge entries: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    // Build the API URL based on whether a project ID is provided
    const apiUrl = `${API_BASE_URL}/knowledge-base`;
    
    // Make the request to the backend API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to create knowledge entry: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}