import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  
  try {
    // Check if id is a valid integer
    const kbIdNum = parseInt(id, 10);
    if (isNaN(kbIdNum) || kbIdNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid knowledge base ID. Must be a positive integer.' },
        { status: 400 }
      );
    }
    
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Make the request to the backend API
    const response = await fetch(`${API_BASE_URL}/knowledge-base/${encodeURIComponent(id)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000) // 10 second timeout
    }).catch(error => {
      console.error('Fetch error:', error);
      return NextResponse.json(
        { error: 'Failed to connect to the server. Please try again later.' },
        { status: 500 }
      );
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || 'Failed to fetch knowledge base entry' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in knowledge base route:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred. Please try again later.' },
      { status: 500 }
    );
  }
}
