// Common types used across the API client

export enum CategoryEnum {
  Exceptions = 'EXCEPTIONS',
  CommonIssues = 'COMMON_ISSUES',
  Misc = 'MISC',
  IgnoredExceptions = 'IGNORED_EXCEPTIONS',
}

export enum StatusEnum {
  Open = 'Open',
  InProgress = 'In Progress',
  Resolved = 'Resolved',
  Closed = 'Closed',
}

export enum ImpactEnum {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Critical = 'Critical',
}

export enum FrequencyEnum {
  Rare = 'Rare',
  Occasional = 'Occasional',
  Frequent = 'Frequent',
  Always = 'Always',
}

export enum SystemNameEnum {
  OMS = 'OMS',
  WMS = 'WMS',
  AUTOMATION = 'AUTOMATION',
  OTHERS = 'OTHERS',
}

export interface User {
  user_id: number;
  name: string;
  email: string;
  created_at: string;
  is_admin: boolean;
  role?: string;
  status: 'Active' | 'Inactive';
  projects?: string[];
}

export interface Project {
  project_id: number;
  project_name: string;
  description: string;
  icon_name: string;
  created_at: string;
  user_count?: number;
}

export interface Issue {
  issue_id: number;
  project_id: number;
  category: CategoryEnum;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name?: SystemNameEnum;
  issue_type?: string;
  status: StatusEnum;
  impact?: ImpactEnum;
  frequency?: FrequencyEnum;
  description: string;
  jira_id?: string;
  jira_link?: string;
  hemants_view?: string;
  created_by: number;
  created_at: string;
  reviewed_by?: number;
  reviewed_at?: string;
  is_favorite?: boolean;
}

export interface Solution {
  solution_id: number;
  issue_id: number;
  category: CategoryEnum;
  provided_by: number;
  provided_by_name?: string;
  solution_text: string;
  upvotes: number;
  created_at: string;
  is_upvoted?: boolean;
}

export interface KnowledgeBase {
  kb_id: number;
  project_id: number;
  title: string;
  content: string;
  solution?: string;
  uploaded_by: number;
  file_url?: string;
  created_at: string;
  author_name?: string;
  verified?: boolean;
  views?: number;
  likes?: number;
  system?: string;
  type?: string;
  is_favorite?: boolean;
  is_liked?: boolean;
}

export interface Favorite {
  favorite_id: number;
  user_id: number;
  issue_id: number;
  created_at: string;
  issue?: Issue;
}

export interface PaginatedResponse<T> {
  data: any;
  items: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface SearchFilters {
  query?: string;
  category?: CategoryEnum | string;
  status?: StatusEnum | string;
  impact?: ImpactEnum | string;
  frequency?: FrequencyEnum | string;
  date_range?: [string, string];
  min_upvotes?: number;
  project_id?: number;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ApiErrorResponse {
  detail: string;
  status_code: number;
  errors?: Record<string, string[]>;
}

export type ApiResponse<T> = T | ApiErrorResponse;

export class ApiError extends Error {
  status: number;
  isAuthError: boolean;
  data?: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.isAuthError = status === 401;
    this.data = data;
    
    // Set the prototype explicitly to ensure instanceof works correctly
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}
