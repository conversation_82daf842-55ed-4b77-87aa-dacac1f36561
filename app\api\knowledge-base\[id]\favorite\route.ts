import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const kb_id = params.id;
    
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Make the request to the backend API
    const response = await fetch(`${API_BASE_URL}/knowledge-base/${kb_id}/favorite`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);
      
      return NextResponse.json(
        { error: `Failed to toggle favorite status: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Return success response with no content
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Knowledge base favorite API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}
